import { AIService } from '../ai/integrations/AIIntegration';
import { VTON_IMAGE_VALIDATION_PROMPT } from '../ai/prompts/vtonImageValidationPrompt';
import { vtonImageValidationSchema, VtonImageValidationResponse } from '../ai/schemas/vtonImageValidationSchema';

/**
 * Utility class for validating images using AI vision models
 * Provides guardrails to prevent abuse and ensure image quality
 */
export class ImageValidationUtil {
  private aiService: AIService;

  constructor() {
    this.aiService = new AIService();
  }

  /**
   * Validate a VTON base image
   * Ensures the image contains a single human in appropriate attire
   * @param imageUrl URL of the image to validate
   * @returns Validation result with error message if invalid
   */
  async validateVtonBaseImage(imageUrl: string): Promise<{ isValid: boolean; errorMessage?: string }> {
    try {
      console.log('[ImageValidationUtil][validateVtonBaseImage] Starting validation for:', imageUrl.substring(0, 50) + '...');

      const userPrompt = 'Validate if this image is suitable for use as a virtual try-on base image.';

      console.log('[ImageValidationUtil][validateVtonBaseImage] Calling Gemini for validation with structured output');
      const response = await this.aiService.getGeminiImageStructuredOutput<VtonImageValidationResponse>(
        imageUrl,
        userPrompt,
        VTON_IMAGE_VALIDATION_PROMPT,
        vtonImageValidationSchema,
        0.3
      );

      console.log('[ImageValidationUtil][validateVtonBaseImage] Validation result:', response);

      return {
        isValid: response.isValid === true,
        errorMessage: response.errorMessage || undefined
      };
    } catch (error) {
      console.error('[ImageValidationUtil][validateVtonBaseImage] Error during validation:', error);
      return {
        isValid: false,
        errorMessage: 'Failed to validate image. Please try again with a different image.'
      };
    }
  }

}

