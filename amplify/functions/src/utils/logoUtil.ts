import sharp from 'sharp';
import * as path from 'path';
import * as fs from 'fs';
import { fileURLToPath } from 'url';

/**
 * Utility class for adding logos to images
 * Used primarily for adding branding to generated VTON images
 */
export class LogoUtil {
  private logoPath: string;
  private logoSizePercentage: number;
  private logoPadding: number;

  constructor() {
    // Get __dirname equivalent for both CommonJS and ES modules
    let currentDir: string;

    // Check if we're in ES module or CommonJS
    if (typeof __dirname !== 'undefined') {
      // CommonJS (Lambda environment)
      currentDir = __dirname;
    } else {
      // ES module (local testing)
      const __filename = fileURLToPath(import.meta.url);
      currentDir = path.dirname(__filename);
    }

    const findLogoPath = (startDir: string): string | null => {
      let current = startDir;
      // Prevent infinite loop by stopping at the root directory
      while (current !== path.parse(current).root) {
        const assetsPath = path.join(current, 'assets', 'monova-logo.png');
        if (fs.existsSync(assetsPath)) {
          return assetsPath;
        }
        const publicPath = path.join(current, 'public', 'monova-logo.png');
        if (fs.existsSync(publicPath)) {
          return publicPath;
        }
        const rootPath = path.join(current, 'monova-logo.png');
        if (fs.existsSync(rootPath)) {
          return rootPath;
        }
        current = path.dirname(current);
      }
      return null;
    };

    const foundLogoPath = findLogoPath(currentDir);

    if (foundLogoPath) {
      console.log(`Logo found at: ${foundLogoPath}`);
      this.logoPath = foundLogoPath;
    } else {
      const defaultPath = path.join(currentDir, '../assets/monova-logo.png');
      console.error(`Logo not found! Falling back to default path: ${defaultPath}`);
      this.logoPath = defaultPath;
    }

    // Logo will be 15% of the image width
    this.logoSizePercentage = 0.15;

    // 10px padding from the edges
    this.logoPadding = 10;
  }

  /**
   * Add logo to an image file and save to a new location
   * @param inputImagePath Path to the input image file
   * @param outputImagePath Path where the output image should be saved
   * @returns Promise<void>
   */
  async addLogoToImage(inputImagePath: string, outputImagePath: string): Promise<void> {
    try {
      console.log('[LogoUtil][addLogoToImage] Starting logo addition process');
      console.log('[LogoUtil][addLogoToImage] Input:', inputImagePath);
      console.log('[LogoUtil][addLogoToImage] Output:', outputImagePath);

      // Validate that the logo file exists
      if (!fs.existsSync(this.logoPath)) {
        throw new Error(`Logo file not found at path: ${this.logoPath}`);
      }

      // Load and get metadata of the input image
      const inputImage = sharp(inputImagePath);
      const inputMetadata = await inputImage.metadata();
      
      if (!inputMetadata.width || !inputMetadata.height) {
        throw new Error('Could not read input image dimensions');
      }

      const imageWidth = inputMetadata.width;
      const imageHeight = inputMetadata.height;

      console.log('[LogoUtil][addLogoToImage] Input image dimensions:', {
        width: imageWidth,
        height: imageHeight,
        format: inputMetadata.format
      });

      // Load and prepare the logo
      const logoImage = sharp(this.logoPath);
      const logoMetadata = await logoImage.metadata();
      
      if (!logoMetadata.width || !logoMetadata.height) {
        throw new Error('Could not read logo dimensions');
      }

      // Calculate logo size (percentage of image width)
      const logoTargetWidth = Math.round(imageWidth * this.logoSizePercentage);
      const logoTargetHeight = Math.round(
        (logoTargetWidth / logoMetadata.width) * logoMetadata.height
      );

      console.log('[LogoUtil][addLogoToImage] Logo will be resized to:', {
        width: logoTargetWidth,
        height: logoTargetHeight
      });

      // Resize the logo
      const resizedLogoBuffer = await logoImage
        .resize(logoTargetWidth, logoTargetHeight, {
          fit: 'inside'
        })
        .toBuffer();

      // Calculate logo position (top right corner)
      const logoLeft = imageWidth - logoTargetWidth - this.logoPadding;
      const logoTop = this.logoPadding;

      console.log('[LogoUtil][addLogoToImage] Logo position (top right):', {
        left: logoLeft,
        top: logoTop
      });

      // Add logo to the image and save
      await inputImage
        .composite([{
          input: resizedLogoBuffer,
          top: logoTop,
          left: logoLeft
        }])
        .jpeg({ quality: 90 })
        .toFile(outputImagePath);

      console.log('[LogoUtil][addLogoToImage] Logo added successfully');
    } catch (error) {
      console.error('[LogoUtil][addLogoToImage] Error adding logo:', error);
      throw new Error(`Failed to add logo to image: ${error instanceof Error ? error.message : error}`);
    }
  }

  /**
   * Add logo to an image buffer and return the modified buffer
   * @param inputBuffer Input image buffer
   * @returns Promise<Buffer> Modified image buffer with logo
   */
  async addLogoToBuffer(inputBuffer: Buffer): Promise<Buffer> {
    try {
      console.log('[LogoUtil][addLogoToBuffer] Starting logo addition to buffer');

      // Validate that the logo file exists
      if (!fs.existsSync(this.logoPath)) {
        throw new Error(`Logo file not found at path: ${this.logoPath}`);
      }

      // Load and get metadata of the input image
      const inputImage = sharp(inputBuffer);
      const inputMetadata = await inputImage.metadata();
      
      if (!inputMetadata.width || !inputMetadata.height) {
        throw new Error('Could not read input image dimensions');
      }

      const imageWidth = inputMetadata.width;
      const imageHeight = inputMetadata.height;

      console.log('[LogoUtil][addLogoToBuffer] Input image dimensions:', {
        width: imageWidth,
        height: imageHeight,
        format: inputMetadata.format
      });

      // Load and prepare the logo
      const logoImage = sharp(this.logoPath);
      const logoMetadata = await logoImage.metadata();
      
      if (!logoMetadata.width || !logoMetadata.height) {
        throw new Error('Could not read logo dimensions');
      }

      // Calculate logo size (percentage of image width)
      const logoTargetWidth = Math.round(imageWidth * this.logoSizePercentage);
      const logoTargetHeight = Math.round(
        (logoTargetWidth / logoMetadata.width) * logoMetadata.height
      );

      console.log('[LogoUtil][addLogoToBuffer] Logo will be resized to:', {
        width: logoTargetWidth,
        height: logoTargetHeight
      });

      // Resize the logo
      const resizedLogoBuffer = await logoImage
        .resize(logoTargetWidth, logoTargetHeight, {
          fit: 'inside'
        })
        .toBuffer();

      // Calculate logo position (top right corner)
      const logoLeft = imageWidth - logoTargetWidth - this.logoPadding;
      const logoTop = this.logoPadding;

      console.log('[LogoUtil][addLogoToBuffer] Logo position (top right):', {
        left: logoLeft,
        top: logoTop
      });

      // Add logo to the image and return buffer
      const outputBuffer = await inputImage
        .composite([{
          input: resizedLogoBuffer,
          top: logoTop,
          left: logoLeft
        }])
        .jpeg({ quality: 90 })
        .toBuffer();

      console.log('[LogoUtil][addLogoToBuffer] Logo added successfully to buffer');
      return outputBuffer;
    } catch (error) {
      console.error('[LogoUtil][addLogoToBuffer] Error adding logo:', error);
      throw new Error(`Failed to add logo to buffer: ${error instanceof Error ? error.message : error}`);
    }
  }
}

