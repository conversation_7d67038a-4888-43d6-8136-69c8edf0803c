import { AIUtil } from '../../utils/aiUtil';
import { OpenWeatherService } from '../../ai/integrations/OpenWeatherIntegration';
import { AIService } from '../../ai/integrations/AIIntegration';
import { ImageStorageService } from '../imageStorageService';
import { StyleProfileService } from '../styleProfileService';
import { OUTFIT_IMAGE_GENERATION_PROMPT } from '../../ai/prompts/outfitImageGenerationPrompt';
import { VTON_IMAGE_GENERATION_PROMPT, VTON_IMAGE_GENERATION_SYSTEM_PROMPT } from '../../ai/prompts/vtonImageGenerationPrompt';
import { isValidUrl } from '../../utils/urlUtil';
import { LogoUtil } from '../../utils/logoUtil';

export class ManychatUtilService {
  private aiUtil: AIUtil;
  private openWeatherService: OpenWeatherService;
  private aiService: AIService;
  private imageStorageService: ImageStorageService;
  private styleProfileService: StyleProfileService;
  private logoUtil: LogoUtil;

  constructor() {
    this.aiUtil = new AIUtil();
    this.openWeatherService = new OpenWeatherService();
    this.aiService = new AIService();
    this.imageStorageService = new ImageStorageService();
    this.styleProfileService = new StyleProfileService();
    this.logoUtil = new LogoUtil();
  }

  /**
   * Get a valid city from user input, or return ERROR if not found
   * @param req { manychatSubscriberId: string, userInput: string }
   * @returns { response: string, message: string }
   */
  async getValidCityFromText(req: { manychatSubscriberId: string, userInput: string }): Promise<{ response: string, message: string }> {
    console.log('[ManychatUtilService][getValidCityFromText] Start', { req });
    try {
      const { userInput } = req;
      console.log('[ManychatUtilService][getValidCityFromText] User input:', userInput);
      // Step 1: Extract city candidates from AIUtil
      const { cities, message: aiMessage } = await this.aiUtil.getCityCandidatesFromText(userInput);
      console.log('[ManychatUtilService][getValidCityFromText] City candidates from AI:', { cities, aiMessage });
      if (!cities || cities.length === 0) {
        console.log('[ManychatUtilService][getValidCityFromText] No city candidates found. Returning ERROR.');
        return { response: 'ERROR', message: aiMessage || "Sorry, I couldn't find any valid city in your input." };
      }
      // Step 2: Check each city with OpenWeather
      for (const city of cities.slice(0, 3)) {
        console.log(`[ManychatUtilService][getValidCityFromText] Checking city with OpenWeather: ${city}`);
        const result = await this.openWeatherService.isValidCity(city);
        console.log(`[ManychatUtilService][getValidCityFromText] OpenWeather result for '${city}':`, result);
        if (result.valid) {
          const cityString = result.city;
          console.log('[ManychatUtilService][getValidCityFromText] Found valid city:', cityString);
          return { response: cityString, message: `Found valid city: ${cityString}` };
        }
      }
      // If none are valid
      console.log('[ManychatUtilService][getValidCityFromText] No valid city found after OpenWeather checks. Returning ERROR.');
      return { response: 'ERROR', message: "Sorry, I couldn't find a valid city that matches your input." };
    } catch (error) {
      console.error('[ManychatUtilService][getValidCityFromText] Error:', error);
      return { response: 'ERROR', message: "Sorry, something went wrong while processing your request." };
    } finally {
      console.log('[ManychatUtilService][getValidCityFromText] End');
    }
  }

  /**
   * Generate an outfit image from text description and save to Azure storage
   * @param req { outfitText: string, userId: string, operationId: string }
   * @returns { imageUrl: string, message: string } or { error: string, message: string }
   */
  async generateOutfitImage(req: { outfitText: string, userId: string, operationId: string }): Promise<{ imageUrl?: string, error?: string, message: string }> {
    console.log('[ManychatUtilService][generateOutfitImage] Start', { req: { ...req, outfitText: req.outfitText.substring(0, 50) + '...' } });
    try {
      const { outfitText, userId, operationId } = req;
      console.log('[ManychatUtilService][generateOutfitImage] Generating image for outfit text:', outfitText.substring(0, 100) + '...');

      // Create the prompt using the template
      const imagePrompt = OUTFIT_IMAGE_GENERATION_PROMPT(outfitText);
      console.log('[ManychatUtilService][generateOutfitImage] Using prompt:', imagePrompt.substring(0, 150) + '...');

      // Generate image using AI service
      const generatedImageUrl = await this.aiService.generateImage(imagePrompt);
      console.log('[ManychatUtilService][generateOutfitImage] Image generated successfully');

      // Create storage path using operationId as the filename
      const storagePath = `users/${userId}/generatedOutfitImages/${operationId}`;

      // Save the generated image to Azure storage
      const storedImageUrl = await this.imageStorageService.uploadImage(generatedImageUrl, storagePath);
      console.log('[ManychatUtilService][generateOutfitImage] Image saved to Azure storage:', storedImageUrl.substring(0, 50) + '...');

      return {
        imageUrl: storedImageUrl,
        message: `Outfit image generated and saved successfully for operation ${operationId}`
      };
    } catch (error) {
      console.error('[ManychatUtilService][generateOutfitImage] Error:', error);
      return {
        error: 'ERROR',
        message: error instanceof Error ? error.message : "Sorry, something went wrong while generating the outfit image."
      };
    } finally {
      console.log('[ManychatUtilService][generateOutfitImage] End');
    }
  }

  /**
   * Generate a virtual try-on (VTON) image from outfit text and base image
   * @param req { outfitText: string, userId: string, operationId: string }
   * @returns { imageUrl: string, message: string } or { error: string, message: string }
   */
  async generateVtonImage(req: { outfitText: string, userId: string, operationId: string }): Promise<{ imageUrl?: string, error?: string, message: string }> {
    console.log('[ManychatUtilService][generateVtonImage] Start', { req: { ...req, outfitText: req.outfitText.substring(0, 50) + '...' } });
    try {
      const { outfitText, userId, operationId } = req;
      console.log('[ManychatUtilService][generateVtonImage] Generating VTON image for outfit text:', outfitText.substring(0, 100) + '...');

      // Fetch user's style profile
      console.log('[ManychatUtilService][generateVtonImage] Fetching style profile for user:', userId);
      const styleProfile = await this.styleProfileService.getProfile(userId);

      if (!styleProfile) {
        console.error('[ManychatUtilService][generateVtonImage] Style profile not found for user:', userId);
        return {
          error: 'ERROR',
          message: `Style profile not found for user: ${userId}`
        };
      }

      // Extract baseImageForVTON from style profile
      const baseVtonImage = styleProfile.baseImageForVTON;

      if (!baseVtonImage) {
        console.error('[ManychatUtilService][generateVtonImage] Base VTON image not found in style profile for user:', userId);
        return {
          error: 'ERROR',
          message: 'Base VTON image not found in your style profile. Please upload a base image first.'
        };
      }

      // Validate the base VTON image URL
      const urlValidationResult = isValidUrl(baseVtonImage);
      if (!urlValidationResult.valid) {
        console.error('[ManychatUtilService][generateVtonImage] Invalid base VTON image URL:', urlValidationResult.error);
        return {
          error: 'ERROR',
          message: 'Invalid base VTON image URL in your style profile.'
        };
      }

      console.log('[ManychatUtilService][generateVtonImage] Using base VTON image:', baseVtonImage.substring(0, 50) + '...');

      // Create the prompt using the template with style profile parameters
      const promptParams = {
        gender: styleProfile.userGender,
        ageGroup: styleProfile.userAge,
        bodyType: styleProfile.userBodyType,
        undertone: styleProfile.userUndertone,
        height: styleProfile.userHeight,
        outfitText: outfitText
      };

      console.log('[ManychatUtilService][generateVtonImage] Prompt parameters:', {
        ...promptParams,
        outfitText: promptParams.outfitText.substring(0, 50) + '...'
      });

      const vtonPrompt = VTON_IMAGE_GENERATION_PROMPT(promptParams);
      console.log('[ManychatUtilService][generateVtonImage] Using VTON prompt:', vtonPrompt.substring(0, 150) + '...');

      // Generate VTON image using Gemini multimodal API
      console.log('[ManychatUtilService][generateVtonImage] Calling Gemini multimodal API for VTON generation');
      const generatedImageResult = await this.aiService.generateImageUsingGeminiMultiModal(
        baseVtonImage,
        vtonPrompt,
        VTON_IMAGE_GENERATION_SYSTEM_PROMPT
      );

      if (!generatedImageResult.imageData) {
        console.error('[ManychatUtilService][generateVtonImage] No image data generated');
        return {
          error: 'ERROR',
          message: 'VTON image generation failed: No image data returned'
        };
      }

      console.log('[ManychatUtilService][generateVtonImage] VTON image generated successfully');

      // Create a temporary file path to save the image data
      const filename = `vton-${operationId}`;
      const tempFilePath = `/tmp/${filename}.jpg`;
      const tempFilePathWithLogo = `/tmp/${filename}-with-logo.jpg`;
      require('fs').writeFileSync(tempFilePath, generatedImageResult.imageData);

      // Add logo to the VTON image before saving to Azure
      console.log('[ManychatUtilService][generateVtonImage] Adding logo to VTON image');
      try {
        await this.logoUtil.addLogoToImage(tempFilePath, tempFilePathWithLogo);
        console.log('[ManychatUtilService][generateVtonImage] Logo added successfully');
      } catch (logoError) {
        console.error('[ManychatUtilService][generateVtonImage] Failed to add logo, proceeding with original image:', logoError);
        // If logo addition fails, use the original image
        require('fs').copyFileSync(tempFilePath, tempFilePathWithLogo);
      }

      // Create storage path using operationId as the filename
      const storagePath = `users/${userId}/vton/${operationId}`;

      // Save the generated image (with logo) to Azure storage
      const storedImageUrl = await this.imageStorageService.uploadImage(tempFilePathWithLogo, storagePath);
      console.log('[ManychatUtilService][generateVtonImage] VTON image saved to Azure storage:', storedImageUrl.substring(0, 50) + '...');

      // Clean up temporary files
      try {
        require('fs').unlinkSync(tempFilePath);
        require('fs').unlinkSync(tempFilePathWithLogo);
        console.log('[ManychatUtilService][generateVtonImage] Temporary files cleaned up');
      } catch (cleanupError) {
        console.warn('[ManychatUtilService][generateVtonImage] Failed to clean up temporary files:', cleanupError);
      }

      return {
        imageUrl: storedImageUrl,
        message: `VTON image generated and saved successfully for operation ${operationId}`
      };
    } catch (error) {
      console.error('[ManychatUtilService][generateVtonImage] Error:', error);
      return {
        error: 'ERROR',
        message: error instanceof Error ? error.message : "Sorry, something went wrong while generating the VTON image."
      };
    } finally {
      console.log('[ManychatUtilService][generateVtonImage] End');
    }
  }
}