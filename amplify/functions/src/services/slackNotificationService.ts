/**
 * Slack Notification Service
 * Sends notifications to <PERSON>lack using incoming webhooks
 */

export class SlackNotificationService {
  private webhookUrl: string;

  constructor() {
    // Slack webhook URL for VTON notifications
    this.webhookUrl = process.env.SLACK_WEBHOOK_URL || 
      '*********************************************************************************';
  }

  /**
   * Send a VTON completion notification to Slack
   * @param userId - The user ID who requested the VTON
   * @param imageUrl - The URL of the generated VTON image
   * @param operationId - The operation ID for tracking
   * @param success - Whether the VTON generation was successful
   */
  async sendVtonCompletionNotification(
    userId: string,
    imageUrl: string,
    operationId: string,
    success: boolean = true
  ): Promise<void> {
    try {
      console.log('[SlackNotificationService] Sending VTON completion notification', {
        userId,
        operationId,
        success,
        imageUrlLength: imageUrl?.length
      });

      const message = this.formatVtonMessage(userId, imageUrl, operationId, success);

      await this.sendMessage(message);

      console.log('[SlackNotificationService] VTON notification sent successfully');
    } catch (error) {
      // Log error but don't throw - we don't want Slack failures to break VTON flow
      console.error('[SlackNotificationService] Failed to send VTON notification:', error);
    }
  }

  /**
   * Format the VTON completion message for Slack
   */
  private formatVtonMessage(
    userId: string,
    imageUrl: string,
    operationId: string,
    success: boolean
  ): any {
    const statusEmoji = success ? '✅' : '❌';
    const statusText = success ? 'Completed Successfully' : 'Failed';

    return {
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: `${statusEmoji} VTON ${statusText}`,
            emoji: true
          }
        },
        {
          type: 'section',
          fields: [
            {
              type: 'mrkdwn',
              text: `*User ID:*\n${userId}`
            },
            {
              type: 'mrkdwn',
              text: `*Operation ID:*\n${operationId}`
            }
          ]
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Image URL:*\n${imageUrl}`
          }
        },
        {
          type: 'image',
          image_url: imageUrl,
          alt_text: `VTON result for ${userId}`
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `Generated at: <!date^${Math.floor(Date.now() / 1000)}^{date_num} {time_secs}|${new Date().toISOString()}>`
            }
          ]
        }
      ]
    };
  }

  /**
   * Send a message to Slack using the webhook
   */
  private async sendMessage(message: any): Promise<void> {
    const response = await fetch(this.webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Slack webhook request failed: ${response.status} - ${errorText}`);
    }

    console.log('[SlackNotificationService] Message sent to Slack successfully');
  }

  /**
   * Send a simple text notification to Slack
   * @param text - The text message to send
   */
  async sendSimpleNotification(text: string): Promise<void> {
    try {
      console.log('[SlackNotificationService] Sending simple notification:', text);

      await this.sendMessage({ text });

      console.log('[SlackNotificationService] Simple notification sent successfully');
    } catch (error) {
      console.error('[SlackNotificationService] Failed to send simple notification:', error);
    }
  }
}

