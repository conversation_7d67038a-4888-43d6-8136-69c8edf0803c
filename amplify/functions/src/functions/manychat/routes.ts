import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { ManyChatError } from "./utils/errors";
import { setShoppableOutfit } from "./controllers/outfits/setShoppableOutfit";
import { setGenAIOutfit } from "./controllers/outfits/setGenAIOutfit";
import { setExistingItemsOutfit } from "./controllers/outfits/setExistingItemsOutfit";
import { reviewMyOutfit } from "./controllers/outfits/reviewMyOutfit";
import { setOutfitImageGenAI } from "./controllers/outfits/setOutfitImageGenAI";
import { setVtonImageGenAI } from "./controllers/outfits/setVtonImageGenAI";

// Import style profile controllers
import { createStyleProfile } from "./controllers/style/createStyleProfile";
import { updateStyleProfile } from "./controllers/style/updateStyleProfile";
import { getStyleProfile } from "./controllers/style/getStyleProfile";
import { analyzeUserSelfie } from "./controllers/style/analyzeUserSelfieController";

// Import apparel controllers
import { createApparel } from "./controllers/apparels/createApparel";
import { getApparels as getApparels } from "./controllers/apparels/getApparels";

// Import util controllers
import { getValidCityFromText } from "./controllers/util/getValidCityFromText";
import { getSSOLink } from "./controllers/sso/getSSOLink";
import { verifySSOToken } from "./controllers/sso/verifySSOToken";

export const handleRoutes = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  console.log("Logging the event here:", JSON.stringify(event));
  const pathParts = event.path.split("/");
  const lastPathPart = pathParts[pathParts.length - 1];
  const secondLastPathPart = pathParts[pathParts.length - 2];

  // Handle sso routes
  if (secondLastPathPart === "sso") {
    switch (event.httpMethod) {
      case "POST":
        if (lastPathPart === "getSSOLink") {
          console.log("[handleRoutes] Routing to getSSOLink controller");
          return await getSSOLink(event);
        } else if (lastPathPart === "verifySSOToken") {
          console.log("[handleRoutes] Routing to verifySSOToken controller");
          return await verifySSOToken(event);
        }
        break;
    }
    throw new ManyChatError(
      `Invalid path or method for sso: ${event.httpMethod} ${event.path}`,
      400,
      "INVALID_PATH_OR_METHOD"
    );
  }

  // Handle util routes
  if (secondLastPathPart === "util") {
    switch (event.httpMethod) {
      case "POST":
        if (lastPathPart === "getValidCityFromText") {
          console.log("[handleRoutes] Routing to getValidCityFromText controller");
          return await getValidCityFromText(event);
        }
        break;
    }
    throw new ManyChatError(
      `Invalid path or method for util: ${event.httpMethod} ${event.path}`,
      400,
      "INVALID_PATH_OR_METHOD"
    );
  }

  // Handle style profile routes
  if (secondLastPathPart === "style") {
    switch (event.httpMethod) {
      case "POST":
        if (lastPathPart === "create") {
          return await createStyleProfile(event);
        } else if (lastPathPart === "analyze-selfie") {
          return await analyzeUserSelfie(event);
        }
        break;
      case "PUT":
        if (lastPathPart === "update") {
          return await updateStyleProfile(event);
        }
        break;
      case "GET":
        if (lastPathPart === "get") {
          return await getStyleProfile(event);
        }
        break;
    }
    throw new ManyChatError(
      `Invalid path or method for style profile: ${event.httpMethod} ${event.path}`,
      400,
      "INVALID_PATH_OR_METHOD"
    );
  }

  // Handle apparel routes
  if (secondLastPathPart === "apparels") {
    switch (event.httpMethod) {
      case "POST":
        if (lastPathPart === "create") {
          return await createApparel(event);
        }
        break;
      case "GET":
        if (lastPathPart === "get") {
          return await getApparels(event);
        }
        break;
    }
    throw new ManyChatError(
      `Invalid path or method for apparels: ${event.httpMethod} ${event.path}`,
      400,
      "INVALID_PATH_OR_METHOD"
    );
  }

  // Handle outfit routes
  if (secondLastPathPart === "outfits") {
    switch (event.httpMethod) {
      case "POST":
        if (lastPathPart === "setExistingItemsOutfit") {
          return await setExistingItemsOutfit(event);
        } else if (lastPathPart === "reviewMyOutfit") {
          return await reviewMyOutfit(event);
        } else if (lastPathPart === "setOutfitImageGenAI") {
          return await setOutfitImageGenAI(event);
        } else if (lastPathPart === "setVtonImageGenAI") {
          return await setVtonImageGenAI(event);
        } else if (lastPathPart === "setShoppableOutfit") {
          return await setShoppableOutfit(event);
        }
        break;
    }
    throw new ManyChatError(
      `Invalid path or method for outfits: ${event.httpMethod} ${event.path}`,
      400,
      "INVALID_PATH_OR_METHOD"
    );
  }

  // Handle existing routes
  switch (event.httpMethod) {
    case "POST":
      if (lastPathPart === "set-genai-outfit") {
        return await setGenAIOutfit(event);
      }
      throw new ManyChatError(
        "Invalid path for POST request",
        400,
        "INVALID_PATH"
      );

    default:
      throw new ManyChatError(
        `Method ${event.httpMethod} not supported`,
        405,
        "METHOD_NOT_ALLOWED"
      );
  }
};