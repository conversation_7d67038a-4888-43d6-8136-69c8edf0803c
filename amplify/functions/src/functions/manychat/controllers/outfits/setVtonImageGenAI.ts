import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { formatResponse } from "../../utils/errors";
import { ManychatUtilService } from "../../../../services/manychat/manychat.util";
import { ManychatCommonService } from "../../../../services/manychat/manychat.common";
import { MANYCHAT_FIELD_IDS } from "../../../../external/manychat/constants";
import { SlackNotificationService } from "../../../../services/slackNotificationService";

/**
 * Controller for generating virtual try-on (VTON) images using GenAI
 * This endpoint generates a VTON image based on outfit text and the user's base VTON image
 * and sets it in Manychat fields
 */
export const setVtonImageGenAI = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  console.log("[setVtonImageGenAI] Starting VTON image generation");

  try {
    // Parse request body
    if (!event.body) {
      console.log("[setVtonImageGenAI] Missing request body");
      return formatResponse(200, {
        success: false,
        message: "Request body is required"
      });
    }

    const requestBody = JSON.parse(event.body);
    console.log("[setVtonImageGenAI] Request body parsed", {
      hasOutfitText: !!requestBody.outfitText,
      hasManychatSubscriberId: !!requestBody.manychatSubscriberId,
      hasUserId: !!requestBody.userId,
      hasOperationId: !!requestBody.operationId
    });

    // Extract required parameters
    const { outfitText, manychatSubscriberId, userId, operationId } = requestBody;

    // Initialize services
    const manychatUtilService = new ManychatUtilService();
    const manychatCommonService = new ManychatCommonService();
    const slackNotificationService = new SlackNotificationService();

    // Validate required fields - for async operations, set error in Manychat instead of throwing
    // First check if we have manychatSubscriberId to set error field, then check other fields
    if (!manychatSubscriberId) {
      console.log("[setVtonImageGenAI] Missing manychatSubscriberId - cannot set Manychat error field");
      return formatResponse(200, {
        success: false,
        message: "Missing required field: manychatSubscriberId"
      });
    }

    // Now check other required fields and set error in Manychat if any are missing
    if (!outfitText || !userId || !operationId) {
      console.log("[setVtonImageGenAI] Missing required fields", {
        hasOutfitText: !!outfitText,
        hasUserId: !!userId,
        hasOperationId: !!operationId
      });

      // Set error status in Manychat for missing required fields
      try {
        await manychatCommonService.setCustomFields(Number(manychatSubscriberId), [
          {
            field_id: MANYCHAT_FIELD_IDS.OUTFIT_IMAGE_LINK,
            field_value: "ERROR"
          }
        ]);
        console.log("[setVtonImageGenAI] Set error status in Manychat for missing required fields");
      } catch (manychatError) {
        console.error("[setVtonImageGenAI] Failed to set error status in Manychat for missing fields:", manychatError);
      }

      return formatResponse(200, {
        success: false,
        message: "Missing required fields: outfitText, userId, or operationId"
      });
    }

    console.log("[setVtonImageGenAI] Processing request", {
      userId,
      manychatSubscriberId,
      operationId
    });

    try {
      // Generate VTON image using the util service
      const result = await manychatUtilService.generateVtonImage({
        outfitText,
        userId,
        operationId
      });

      if (result.error) {
        // VTON image generation failed - set error status in Manychat
        console.log(`[setVtonImageGenAI] VTON image generation failed for user ${userId}:`, result.message);

        await manychatCommonService.setCustomFields(Number(manychatSubscriberId), [
          {
            field_id: MANYCHAT_FIELD_IDS.OUTFIT_IMAGE_LINK,
            field_value: "ERROR"
          }
        ]);

        console.log(`[setVtonImageGenAI] Set error status in Manychat for user ${userId}`);

        // Send Slack notification for failed VTON
        try {
          await slackNotificationService.sendVtonCompletionNotification(
            userId,
            `Error: ${result.message}`,
            operationId,
            false
          );
          console.log(`[setVtonImageGenAI] Slack error notification sent for user ${userId}`);
        } catch (slackError) {
          console.error(`[setVtonImageGenAI] Failed to send Slack error notification for user ${userId}:`, slackError);
        }
      } else if (result.imageUrl) {
        // VTON image generation succeeded - set image URL in Manychat
        console.log(`[setVtonImageGenAI] VTON image generation succeeded for user ${userId}`);

        await manychatCommonService.setCustomFields(Number(manychatSubscriberId), [
          {
            field_id: MANYCHAT_FIELD_IDS.OUTFIT_IMAGE_LINK,
            field_value: result.imageUrl
          }
        ]);

        console.log(`[setVtonImageGenAI] Set VTON image URL in Manychat for user ${userId}`);

        // Send Slack notification for successful VTON completion
        try {
          await slackNotificationService.sendVtonCompletionNotification(
            userId,
            result.imageUrl,
            operationId,
            true
          );
          console.log(`[setVtonImageGenAI] Slack notification sent for user ${userId}`);
        } catch (slackError) {
          // Log but don't fail the request if Slack notification fails
          console.error(`[setVtonImageGenAI] Failed to send Slack notification for user ${userId}:`, slackError);
        }
      }

      // Return success response (always 200 for Manychat compatibility)
      return formatResponse(200, {
        success: !result.error,
        message: result.message,
        ...(result.imageUrl && { imageUrl: result.imageUrl })
      });

    } catch (serviceError) {
      // Handle any service errors by setting error status in Manychat
      console.error(`[setVtonImageGenAI] Service error for user ${userId}:`, serviceError);
      
      try {
        await manychatCommonService.setCustomFields(Number(manychatSubscriberId), [
          {
            field_id: MANYCHAT_FIELD_IDS.OUTFIT_IMAGE_LINK,
            field_value: "ERROR"
          }
        ]);
        console.log(`[setVtonImageGenAI] Set error status in Manychat after service error for user ${userId}`);
      } catch (manychatError) {
        console.error(`[setVtonImageGenAI] Failed to set error status in Manychat for user ${userId}:`, manychatError);
      }

      // Return 200 with error message for Manychat compatibility
      return formatResponse(200, {
        success: false,
        message: serviceError instanceof Error ? serviceError.message : "An unexpected error occurred"
      });
    }

  } catch (error) {
    // Handle any unexpected errors
    console.error("[setVtonImageGenAI] Unexpected error:", error);
    
    // Return 200 with error message for Manychat compatibility
    return formatResponse(200, {
      success: false,
      message: error instanceof Error ? error.message : "An unexpected error occurred"
    });
  }
};

