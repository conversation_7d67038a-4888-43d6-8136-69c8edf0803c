import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { formatResponse } from "../../utils/errors";
import { ManychatCreateStyleProfileRequest, ManychatResponse } from "../../../../types/manychat";
import { manychatStyleService } from "../../../../services/manychat";
import { manychatCommonService } from "../../../../services/manychat";
import { MANYCHAT_FIELD_IDS, MANYCHAT_STATUS, MANYCHAT_BOOLEAN } from "../../../../external/manychat/constants";
import { isValidUrl } from "../../../../utils/urlUtil";

export const createStyleProfile = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  // Initialize response object
  const response: ManychatResponse = {
    status: MANYCHAT_STATUS.FAILURE,
    message: "",
    timestamp: new Date().toISOString()
  };

  try {
    if (!event.body) {
      response.message = "Request body is required";
      response.errorMessage = "MISSING_REQUEST_BODY";
      return formatResponse(200, response);
    }

    let input: ManychatCreateStyleProfileRequest;
    try {
      input = JSON.parse(event.body);
    } catch (e) {
      response.message = "Invalid JSON in request body";
      response.errorMessage = "INVALID_JSON";
      return formatResponse(200, response);
    }

    // Validate minimal required fields
    const minimalRequiredFields = [
      "userId",
      "userGender",
      "userUndertone"
    ];

    const missingMinimalFields = minimalRequiredFields.filter(field => !(field in input));
    if (missingMinimalFields.length > 0) {
      response.message = `Missing required fields: ${missingMinimalFields.join(", ")}`;
      response.errorMessage = "MISSING_REQUIRED_FIELDS";
      return formatResponse(200, response);
    }

    // Fields that can be derived from selfie analysis
    const selfieDerivableFields = [
      "userSkinTone",
      "userEyeColor",
      "userHairColor",
      "userContrast",
      "userSeason"
    ];

    // Fields that must be provided by the client
    const clientRequiredFields = [
      "userAge",
      "userBodyType",
      "userHeight"
    ];

    // Validate client required fields - these cannot be derived from selfie
    const missingClientRequiredFields = clientRequiredFields.filter(field => !(field in input));
    if (missingClientRequiredFields.length > 0) {
      response.message = `Missing required fields: ${missingClientRequiredFields.join(", ")}. These fields cannot be derived from a profile image and must be provided.`;
      response.errorMessage = "MISSING_REQUIRED_FIELDS";
      return formatResponse(200, response);
    }

    // Validate profile image URL if provided
    if (input.userProfileImageUrl) {
      const urlValidationResult = isValidUrl(input.userProfileImageUrl);
      if (!urlValidationResult.valid) {
        console.log(`[createStyleProfile] Invalid profile image URL: ${urlValidationResult.error}. Treating as missing.`);
        // Remove the invalid URL from the input object completely
        delete input.userProfileImageUrl;
      }
    }

    // Validate base image for VTON URL if provided
    if (input.baseImageForVTON) {
      const urlValidationResult = isValidUrl(input.baseImageForVTON);
      if (!urlValidationResult.valid) {
        console.log(`[createStyleProfile] Invalid base image for VTON URL: ${urlValidationResult.error}. Treating as missing.`);
        // Remove the invalid URL from the input object completely
        delete input.baseImageForVTON;
      }
    }

    // Check for missing selfie-derivable fields
    // If selfie is not available, we'll skip these fields and proceed
    const missingSelfieDerivableFields = selfieDerivableFields.filter(field => !(field in input));
    if (missingSelfieDerivableFields.length > 0 && input.userProfileImageUrl) {
      console.log(`[createStyleProfile] Missing selfie-derivable fields will be extracted from profile image: ${missingSelfieDerivableFields.join(", ")}`);
    } else if (missingSelfieDerivableFields.length > 0) {
      console.log(`[createStyleProfile] Missing selfie-derivable fields but no profile image provided. Proceeding without these fields: ${missingSelfieDerivableFields.join(", ")}`);
    }

    console.log("[CreateStyleProfileController] Processing request:", {
      userId: input.userId,
      userGender: input.userGender,
      userAge: input.userAge,
      hasProfileImage: !!input.userProfileImageUrl
    });

    // Extract manychatSubscriberId if provided
    const manychatSubscriberId = input.manychatSubscriberId ? Number(input.manychatSubscriberId) : null;

    try {
      // Call the service method to create the style profile
      const profile = await manychatStyleService.createStyleProfile(input);

      // Update Manychat field if subscriberId is provided
      if (manychatSubscriberId) {
        await manychatCommonService.setCustomFields(manychatSubscriberId, [
          {
            field_id: MANYCHAT_FIELD_IDS.STYLE_PROFILE_EXISTS,
            field_value: MANYCHAT_BOOLEAN.TRUE
          }
        ]);
      }

      // Set success response
      response.status = MANYCHAT_STATUS.SUCCESS;
      response.message = "Style profile created successfully";
      response.data = profile;

      return formatResponse(200, response);
    } catch (serviceError) {
      console.error("[CreateStyleProfileController] Service error:", serviceError);

      // Handle profile already exists case
      if (serviceError instanceof Error && serviceError.message.includes("Profile already exists")) {
        response.status = MANYCHAT_STATUS.FAILURE;
        response.message = serviceError.message;
        response.errorMessage = "PROFILE_EXISTS";
        return formatResponse(200, response);
      }

      // Handle other service errors
      response.message = "Failed to create style profile";
      response.errorMessage = serviceError instanceof Error ? serviceError.message : String(serviceError);

      return formatResponse(200, response);
    }
  } catch (error) {
    console.error("[CreateStyleProfileController] Unexpected error:", error);

    // Handle unexpected errors
    response.message = "An unexpected error occurred";
    response.errorMessage = error instanceof Error ? error.message : String(error);

    return formatResponse(200, response);
  }
};
