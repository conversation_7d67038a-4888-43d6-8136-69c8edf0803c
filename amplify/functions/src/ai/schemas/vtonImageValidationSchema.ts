/**
 * JSON schema for VTON image validation response using Gemini structured output
 * Based on the vtonImageValidationPrompt.ts requirements
 */
import { Type } from "@google/genai";

export const vtonImageValidationSchema = {
  type: Type.OBJECT,
  properties: {
    isValid: {
      type: Type.BOOLEAN,
      description: "Whether the image is valid for use as a VTON base image"
    },
    errorMessage: {
      type: Type.STRING,
      description: "Specific error message if the image is invalid, null if valid",
      nullable: true
    }
  },
  required: ["isValid", "errorMessage"]
};

/**
 * TypeScript interface for the VTON image validation response
 */
export interface VtonImageValidationResponse {
  isValid: boolean;
  errorMessage: string | null;
}

