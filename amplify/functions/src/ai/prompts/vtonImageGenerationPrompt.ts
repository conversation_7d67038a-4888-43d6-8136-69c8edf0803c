/**
 * Prompt for generating virtual try-on (VTON) images using Gemini multimodal API.
 *
 * This prompt takes a base image of a person and generates a virtual try-on image
 * with the specified outfit, maintaining the person's body parameters.
 */

export interface VtonPromptParams {
  gender: string;
  ageGroup: string;
  bodyType: string;
  undertone: string;
  height: string;
  outfitText: string;
}

export const VTON_IMAGE_GENERATION_PROMPT = (params: VtonPromptParams) => {
  const prompt = `Create a virtual try-on of the individual in the image on a plain white background in size 512x512 like a photo shoot with the individual standing in a casual position with their body parameters and clothes details as mentioned below. We need to make sure each and every element of the outfit is there in the image.

• Gender: ${params.gender}
• Age Group: ${params.ageGroup}
• Body Type: ${params.bodyType}
• Undertone: ${params.undertone}
• Height: ${params.height}

Outfit Details:
${params.outfitText}`;

  return prompt;
};

export const VTON_IMAGE_GENERATION_SYSTEM_PROMPT =
  `You are an expert AI image generation system specialized in creating realistic virtual try-on images. Your task is to generate a photo-realistic image of the person in the provided image wearing the specified outfit, while maintaining their physical characteristics and body parameters.`;

