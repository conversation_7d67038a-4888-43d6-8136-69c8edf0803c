export type Gender = "MALE" | "FEMALE";

export type Undertone = "WARM" | "COOL";

export type SkinTone = "DARK" | "MEDIUM" | "FAIR";

export type MaleBodyType =
  | "OVAL"
  | "RECTANGLE"
  | "TRIANGLE"
  | "INVERTED_TRIANGLE"
  | "TRAPEZOID";

export type FemaleBodyType =
  | "APPLE"
  | "RECTANGLE"
  | "HOURGLASS"
  | "INVERTED_TRIANGLE"
  | "PEAR";

export type BodyType = MaleBodyType | FemaleBodyType;

export type Height = "SHORT" | "MEDIUM" | "TALL";

export type EyeColor = "BLACK" | "GREY" | "BROWN" | "BLUE" | "GREEN" | "AMBER";

export type HairColor = "BLACK" | "BROWN" | "GREY" | "RED" | "BLONDE" | "WHITE";

export type Contrast = "DARK" | "LIGHT";

export type Season = "SUMMER" | "WINTER" | "SPRING" | "AUTUMN";

export type UserAge =
  | "AGE_0_20"
  | "AGE_21_24"
  | "AGE_25_30"
  | "AGE_31_36"
  | "AGE_36_45"
  | "AGE_46_55"
  | "AGE_55_99";

export interface StyleProfileStore {
  userId: string;
  userAge: UserAge;
  userGender: Gender;
  userUndertone: Undertone;
  userBodyType: BodyType;
  userHeight: Height;
  userHeightMetric?: string;
  // Selfie-derivable fields are now optional
  userSkinTone?: SkinTone;
  userEyeColor?: EyeColor;
  userHairColor?: HairColor;
  userContrast?: Contrast;
  userSeason?: Season;
  userProfileImageUrl?: string;
  styleGuide?: StyleGuideResponse;
  baseLocation?: string;
  baseImageForVTON?: string;
}

export interface CreateStyleGuideInput {
  userAge: UserAge;
  userGender: Gender;
  userUndertone: Undertone;
  userBodyType: BodyType;
  userHeight: Height;
  userHeightMetric?: string;
  // Selfie-derivable fields are now optional
  userSkinTone?: SkinTone;
  userEyeColor?: EyeColor;
  userHairColor?: HairColor;
  userContrast?: Contrast;
  userSeason?: Season;
  userProfileImageUrl?: string;
}

export interface CreateStyleProfileInput extends StyleProfileStore {}

export interface UpdateStyleProfileInput {
  userAge?: UserAge;
  userGender?: Gender;
  userUndertone?: Undertone;
  userSkinTone?: SkinTone;
  userBodyType?: BodyType;
  userHeight?: Height;
  userHeightMetric?: string; //not sure why this is needed
  userEyeColor?: EyeColor;
  userHairColor?: HairColor;
  userContrast?: Contrast;
  userSeason?: Season;
  userProfileImageUrl?: string;
  baseLocation?: string;
  baseImageForVTON?: string;
}

export interface AnalyzeImageResponse {
  isValid: boolean;
  userContrast?: Contrast;
  userHairColor?: HairColor;
  userEyeColor?: EyeColor;
  userSkinTone?: SkinTone;
  errorMessage?: string | null;
}

export interface SelfieAnalysisResponse extends AnalyzeImageResponse {
  userSeason?: Season;
}
export interface ColorSeasonResponse {
  SeasonSuggestions: string[];
  ColorPaletteUrl: string;
  SeasonTags: string[];
}
export interface HeightResponse {
  HeightSuggestions: string;
  HeightTags: string[];
}

export interface BodyTypeResponse {
  BodyTypeSuggestions: string;
  BodyTypeTags: {
    FitTags: string[];
    PatternTags: string[];
  };
}

export interface StyleGuideResponse {
  StyleGuideMediaUrls: string[];
  HeightOutput: HeightResponse;
  BodyOutput: BodyTypeResponse;
  SeasonOutput: ColorSeasonResponse;
}

/**
 * These are Manychat specific types.
 * In future, these will be deprecated to use the standard types for style
 */
export interface ManychatStyleProfile {
  userBodyType: BodyType;
  userUndertone: Undertone;
  userAge: UserAge;
  userGender: Gender;
  userHeight: Height;
}
